// Simple test to verify the weekly data retrieval function
const { getRowsWeekly } = require('./lambda/dynamo');

// Mock environment variables
process.env.PROPERTY_ID = 'URW-AIRPORT';

// Test the weekly function logic
function testWeeklyDateRange() {
    console.log('Testing weekly date range calculation...');
    
    // Simulate the logic from getRowsWeekly
    const start = new Date();
    start.setDate(start.getDate() - 7); // Get records from last 7 days
    start.setHours(0, 0, 0, 0);
    
    const now = new Date();
    const daysDifference = Math.floor((now - start) / (1000 * 60 * 60 * 24));
    
    console.log(`Current date: ${now.toISOString()}`);
    console.log(`Weekly start date: ${start.toISOString()}`);
    console.log(`Days difference: ${daysDifference}`);
    
    if (daysDifference === 7) {
        console.log('✅ Weekly date range calculation is correct');
        return true;
    } else {
        console.log('❌ Weekly date range calculation is incorrect');
        return false;
    }
}

// Test the daily function logic for comparison
function testDailyDateRange() {
    console.log('\nTesting daily date range calculation...');
    
    // Simulate the logic from getRows
    const start = new Date();
    start.setDate(start.getDate() - 1); // Get records from last 1 day
    start.setHours(0, 0, 0, 0);
    
    const now = new Date();
    const daysDifference = Math.floor((now - start) / (1000 * 60 * 60 * 24));
    
    console.log(`Current date: ${now.toISOString()}`);
    console.log(`Daily start date: ${start.toISOString()}`);
    console.log(`Days difference: ${daysDifference}`);
    
    if (daysDifference === 1) {
        console.log('✅ Daily date range calculation is correct');
        return true;
    } else {
        console.log('❌ Daily date range calculation is incorrect');
        return false;
    }
}

// Run tests
console.log('=== Testing Date Range Calculations ===');
const weeklyTest = testWeeklyDateRange();
const dailyTest = testDailyDateRange();

if (weeklyTest && dailyTest) {
    console.log('\n🎉 All tests passed! The implementation should work correctly.');
} else {
    console.log('\n❌ Some tests failed. Please review the implementation.');
}
