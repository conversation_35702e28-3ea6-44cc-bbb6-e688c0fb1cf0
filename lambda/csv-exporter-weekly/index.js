const { getRowsWeekly } = require("../dynamo");
const { exportToCsvAndS3, downloadFromS3, getExistingRecords } = require("../csv-exporter/fileExport");
const { sendEmail } = require("../csv-exporter/ses");
const { DYNAMO_TABLES, FORM_TYPES, REGISTER_NOW_FILE } = require("../constants");

exports.handler = async () => {
  try {
    // Get weekly data from dynamoDB for Register Now form only
    console.log("Fetching weekly data from DynamoDB for Register Now form...");
    const registerNow = await getRowsWeekly(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.REGISTER_NOW);
    
    // Get existing files from S3
    console.log("Downloading existing Register Now file from S3 ...");
    await downloadFromS3(REGISTER_NOW_FILE);
    
    const existingRegisterNow = await getExistingRecords(REGISTER_NOW_FILE);

    console.log("Processing Register Now records for weekly export...");
    const registerNowToWrite = getRegisterNowRecordsToWrite(registerNow.Items, existingRegisterNow);
    console.log(`Register Now records to write: ${registerNowToWrite.length}`);

    console.log("Saving new weekly data to downloaded file ... ");
    // Save new data to downloaded file and upload back to S3
    const result = await exportToCsvAndS3(registerNowToWrite, REGISTER_NOW_FILE);
    console.log(`Successfully completed weekly export operation`);

    console.log("Sending email for weekly exported file...");
    const emailResult = await sendEmail(result);
    console.log(`Successfully sent weekly email`);

    return {
      statusCode: 200,
      body: JSON.stringify({ emailResult, result }),
    };
  } catch (error) {
    console.error("Error in weekly CSV exporter handler:", error);
    console.error("Error stack:", error.stack);

    // Log additional context for debugging
    console.error("Error details:", {
      message: error.message,
      name: error.name,
      stack: error.stack
    });

    return {
      statusCode: 500,
      body: JSON.stringify({
        error: error.message,
        details: "Check CloudWatch logs for more information"
      }),
    };
  }
};

const getRegisterNowRecordsToWrite = (records, existingRecords) => {
  let existingRegisterNowRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        createdAt: item.Date,
        responseId: item.ResponseId,
        propertyId: item.Airport,
        firstName: item.Firstname,
        lastName: item.Lastname,
        email: item.Email,
        mobile: item.Mobile,
        company: item["Company Name"],
        website: item.Website,
        "bi/architectureDesignEngineering": item["Design"],
        "bi/construction": item["Construction"],
        "bi/concessions": item["Concessions"],
        "bi/other": item["Other"],
        "bi/services": item["Services"],
        "mi/chicago": item["Chicago (ORD)"],
        "mi/losAngles": item["Los Angeles (LAX)"],
        "mi/newYork": item["New York (JFK T8 & New Terminal One)"],
        "cd/womanOwned": item["Woman-owned"],
        "cd/minorityOwned": item["Minority-owned"],
        "cd/veteranOwned": item["Veteran-owned"],
        "cd/acdbe": item["ACDBE"],
        "cd/other": item["Other"],
        otherInformation: item["Anything you would like us to know"],
        registerNowEmailUpdates: item["Email Updates"],
      };
    });

  const recordsToWrite = [...records, ...existingRegisterNowRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};
