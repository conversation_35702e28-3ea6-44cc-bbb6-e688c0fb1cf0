const db = require('./base')

const putRow = (tableName, item) => {
    const now = new Date().toISOString()
    const params = {
        TableName: tableName,
        Item: { ...item, propertyId: process.env.PROPERTY_ID, createdAt: now, }
    }
    return db.docClient.put(params).promise()
}

const getRows = (tableName, formType = '') => {
    const start = new Date()
    start.setDate(start.getDate() - 1);
    start.setHours(0, 0, 0, 0);

    const params = {
        TableName: tableName,
        IndexName: 'propertyId-createdAt-index',
        KeyConditionExpression: 'propertyId = :propertyId AND createdAt >= :createdAt',
        ExpressionAttributeValues: {
            ':propertyId': process.env.PROPERTY_ID,
            ':createdAt': start.toISOString(),
        },
    }
    if (formType) {
        params.FilterExpression = 'formType = :formType';
        params.ExpressionAttributeValues = { ...params.ExpressionAttributeValues, ':formType': formType }
    }

    return db.docClient.query(params).promise()
}

const getRowsWeekly = (tableName, formType = '') => {
    const start = new Date()
    start.setDate(start.getDate() - 7); // Get records from last 7 days
    start.setHours(0, 0, 0, 0);

    const params = {
        TableName: tableName,
        IndexName: 'propertyId-createdAt-index',
        KeyConditionExpression: 'propertyId = :propertyId AND createdAt >= :createdAt',
        ExpressionAttributeValues: {
            ':propertyId': process.env.PROPERTY_ID,
            ':createdAt': start.toISOString(),
        },
    }
    if (formType) {
        params.FilterExpression = 'formType = :formType';
        params.ExpressionAttributeValues = { ...params.ExpressionAttributeValues, ':formType': formType }
    }

    return db.docClient.query(params).promise()
}

module.exports = { putRow, getRows, getRowsWeekly }