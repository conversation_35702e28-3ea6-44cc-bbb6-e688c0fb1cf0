const { getRows } = require("../dynamo");
const { exportToCsvAndS3, downloadFromS3, getExistingRecords } = require("./fileExport");
const { sendEmail } = require("./ses");
const { DYNAMO_TABLES, FORM_TYPES, DAILY_FILES } = require("../constants");

exports.handler = async () => {
  try {
    // Get data from dynamoDB (excluding Register Now - handled by weekly exporter)
    console.log("Fetching daily data from DynamoDB ...");
    const promises = [
      getRows(DYNAMO_TABLES.CONTACT_RESPONSE),
      getRows(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.STAY_UP_TO_DATE),
      // getRows(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.BUSINESS_OPPORTUNITY),
      // getRows(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.EMPLOYMENT_OPPORTUNITY),
    ];

    const [contactUs, stayUpToDate] = await Promise.all(promises);
    // Get existing files from S3
    console.log("Downloading existing files from S3 ...");
    const downloads = DAILY_FILES.map((file) => downloadFromS3(file));

    await Promise.all(downloads);

    const existingCSVRecords = DAILY_FILES.map((file) => getExistingRecords(file));

    // Only Contact Us and Stay Up to Date for daily exports
    const [existingContactUs, existingStayUptoDate] = await Promise.all(existingCSVRecords);

    console.log("Processing records for daily export...");
    const contactUsRecords = getContactUsRecordsToWrite(contactUs.Items, existingContactUs);
    console.log(`Contact Us records to write: ${contactUsRecords.length}`);

    const stayUpToDateRecords = getStayUpToDateRecordsToWrite(stayUpToDate.Items, existingStayUptoDate);
    console.log(`Stay Up To Date records to write: ${stayUpToDateRecords.length}`);

    // const registerNowHoltToWrite = getRegisterNowRecordsToWrite(registerNow.Items, existingRegisterNowHolt);
    // const registerNowLeasingToWrite = getRegisterNowRecordsToWrite(registerNow.Items, existingRegisterNowLeasing);
    // const registerNowOpsToWrite = getRegisterNowRecordsToWrite(registerNow.Items, existingRegisterNowOps);

    // const ntoBusinessToWrite = getNtoBusinessOpportunitiesRecordsToWrite(ntoBusiness.Items, existingNtoBusiness);
    // console.log(`NTO Business records to write: ${ntoBusinessToWrite.length}`);

    // const ntoEmploymentToWrite = getNtoEmploymentOpportunitiesRecordsToWrite(ntoEmployment.Items, existingNtoEmployment);
    // console.log(`NTO Employment records to write: ${ntoEmploymentToWrite.length}`);


    console.log("Saving new daily data to downloaded files ... ");
    // Save new data to downloaded files and upload back to S3
    const exports = DAILY_FILES.map((file) => {
      console.log(`Processing file: ${file.fileName} with form type: ${file.form}`);

      switch (file.form) {
        case FORM_TYPES.CONTACT_US:
          return exportToCsvAndS3(contactUsRecords, file);

        case FORM_TYPES.STAY_UP_TO_DATE:
          return exportToCsvAndS3(stayUpToDateRecords, file);

        // case FORM_TYPES.EMPLOYMENT_OPPORTUNITY:
        // return exportToCsvAndS3(ntoEmploymentToWrite, file);

        // case FORM_TYPES.BUSINESS_OPPORTUNITY:
        //   return exportToCsvAndS3(ntoBusinessToWrite, file);

        default:
          console.error(`Unknown form type: ${file.form} for file: ${file.fileName}`);
          // Return a rejected promise to maintain consistency with Promise.all
          return Promise.reject(new Error(`Unknown form type: ${file.form} for file: ${file.fileName}`));
      }
    });

    console.log(`Waiting for ${exports.length} daily export operations to complete...`);
    const results = await Promise.all(exports);
    console.log(`Successfully completed ${results.length} daily export operations`);

    console.log("Sending emails for daily exported files...");
    const emails = results.map((r) => sendEmail(r));

    const emailResult = await Promise.all(emails);
    console.log(`Successfully sent ${emailResult.length} daily emails`);

    return {
      statusCode: 200,
      body: JSON.stringify({ emailResult, results }),
    };
  } catch (error) {
    console.error("Error in CSV exporter handler:", error);
    console.error("Error stack:", error.stack);

    // Log additional context for debugging
    console.error("Error details:", {
      message: error.message,
      name: error.name,
      stack: error.stack
    });

    return {
      statusCode: 500,
      body: JSON.stringify({
        error: error.message,
        details: "Check CloudWatch logs for more information"
      }),
    };
  }
};

const getContactUsRecordsToWrite = (records, existingRecords) => {
  let existingContactUsRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        company: item.Company,
        createdAt: item.Date,
        email: item.Email,
        firstName: item.Firstname,
        lastName: item.Lastname,
        message: item.Message,
        mobile: item.Mobile,
        propertyId: item.Airport,
        emailUpdates: item["Email Updates"],
      };
    });

  const recordsToWrite = [...records, ...existingContactUsRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getStayUpToDateRecordsToWrite = (records, existingRecords) => {
  let existingStayUpToDateRecords = existingRecords
    .filter((item) => item.Mobile != "")
    .map((item) => {
      return {
        createdAt: item.Date,
        email: item.Email,
        propertyId: item.Airport,
        responseId: item.ResponseId,
      };
    });

  const recordsToWrite = [...records, ...existingStayUpToDateRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getRegisterNowRecordsToWrite = (records, existingRecords) => {
  let existingRegisterNowRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        createdAt: item.Date,
        responseId: item.ResponseId,
        propertyId: item.Airport,
        firstName: item.Firstname,
        lastName: item.Lastname,
        email: item.Email,
        mobile: item.Mobile,
        company: item["Company Name"],
        website: item.Website,
        "bi/architectureDesignEngineering": item["Design"],
        "bi/construction": item["Construction"],
        "bi/concessions": item["Concessions"],
        "bi/other": item["Other"],
        "bi/services": item["Services"],
        "mi/chicago": item["Chicago (ORD)"],
        "mi/losAngles": item["Los Angeles (LAX)"],
        "mi/newYork": item["New York (JFK T8 & New Terminal One)"],
        "cd/womanOwned": item["Woman-owned"],
        "cd/minorityOwned": item["Minority-owned"],
        "cd/veteranOwned": item["Veteran-owned"],
        "cd/acdbe": item["ACDBE"],
        "cd/other": item["Other"],
        otherInformation: item["Anything you would like us to know"],
        registerNowEmailUpdates: item["Email Updates"],
      };
    });

  const recordsToWrite = [...records, ...existingRegisterNowRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getNtoBusinessOpportunitiesRecordsToWrite = (records, existingRecords) => {
  let existingBusinessOpportunitiesRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        aboutBusiness: item["About Business"],
        advancedNetworkNews: item["Advanced Network News"],
        airportExperience: item["Airport Experience"],
        "bi/architectureDesignEngineering": item["Design"],
        "bi/construction": item["Construction"],
        "bi/concessions": item["Concessions"],
        "bi/other": item["Other"],
        "bi/services": item["Services"],
        businessAddress1: item["Business Address 1"],
        businessAddress2: item["Business Address 2"],
        "cert/acdbe": item["ACDBE - Airport Concessions Disadvantaged Business Enterprise"],
        "cert/lbe": item["LBE - Local Business Enterprise"],
        "cert/mbe": item["MBE - Minority Business Enterprise"],
        "cert/sdvosb": item["SDVOSB - Service-Disabled Veteran-Owned Small Businesses"],
        "cert/wbe": item["WBE - Women Business Enterprise"],
        "cert/sbe": item["SBE - Small Business Enterprise"],
        "cert/notCertified": item["Not Certified"],
        "cert/other": item["Other"],
        city: item.City,
        companyName: item["Company Name"],
        createdAt: item.Date,
        email: item.Email,
        emailUpdates: item["Email Updates"],
        firstName: item.Firstname,
        lastName: item.Lastname,
        mobile: item.Mobile,
        propertyId: item.Airport,
        responseId: item.ResponseId,
        state: item.State,
        termsAndConditions: item["Terms And Conditions"],
        textUpdates: item["Text Updates"],
        website: item.Website,
        yearsInBusiness: item["Years In Business"],
        zipCode: item.ZipCode,
        locale: item.Locale,
      };
    });

  const recordsToWrite = [...records, ...existingBusinessOpportunitiesRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getNtoEmploymentOpportunitiesRecordsToWrite = (records, existingRecords) => {
  let existingEmploymentOpportunitiesRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        advancedNetworkNews: item["Advanced Network News"],
        airportEmploymentExperience: item["Airport Employment Experience"],
        city: item["City"],
        createdAt: item["Date"],
        "ei/construction": item["Construction"],
        "ei/foodAndBeverage": item["Food & Beverage"],
        "ei/maintenance": item["Maintenance"],
        "ei/professionalServices": item["Professional Services"],
        "ei/retail": item["Retail"],
        email: item["Email"],
        emailUpdates: item["Email Updates"],
        firstName: item["Firstname"],
        homeAddress1: item["Home Address 1"],
        homeAddress2: item["Home Address 2"],
        lastName: item["Lastname"],
        mobile: item["Mobile"],
        propertyId: item["Airport"],
        responseId: item["ResponseId"],
        state: item["State"],
        termsAndConditions: item["Terms And Conditions"],
        textUpdates: item["Text Updates"],
        zipCode: item["ZipCode"],
        locale: item["Locale"],
      };
    });

  const recordsToWrite = [...records, ...existingEmploymentOpportunitiesRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};
