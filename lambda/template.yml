AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: URW Airports contact forms handler

Resources:
  ApiGatewayApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: test
      Cors:
        AllowMethods: "'POST, GET, OPTIONS'"
        AllowHeaders: "'*'"
        AllowOrigin: "'*'"
        MaxAge: "'600'"
        AllowCredentials: true
  ApiFunction:
    Type: "AWS::Serverless::Function"
    Properties:
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /contact
            Method: ANY
            Auth:
              Authorizer: NONE
      Handler: contact-form/index.handler
      Runtime: nodejs22.x
      Timeout: 300
      Environment:
        Variables:
          PROPERTY_ID: "URW-AIRPORT"
          CONTACT_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-contact-response"
          OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-opportunity-response"
          MAILCHIMP_API_KEY: *************************************
          MAILCHIMP_API_SERVER: us15
          # MAILCHIMP_AUDIENCE_ID: 5f1ebded7e
          MAILCHIMP_AUDIENCE_ID: 718a6cfd39
          CONTENTFUL_SPACE_ID: vmx07p43zh3v
          CONTENTFUL_ENVIRONMENT: demo
          CONTENTFUL_ACCESS_TOKEN: a_yrH2nLfZs7CPotl9C2UcdJFbJL-Sara1kykbaJZTQ
          SITE_URL: "https://d25cs9j6ipjfjp.cloudfront.net"
          MAILCHIMP_TRANSACTIONAL_FROM_EMAIL: "<EMAIL>"
          MAILCHIMP_TRANSACTIONAL_API_KEY: "md-g56ItTTjBDWKAEDBmrUqmw"
          NODE_ENV: "local" # Only for local development
          SOURCE_EMAIL: "<EMAIL>"
          TO_EMAILS: "<EMAIL>"
          CONTACT_US_FORM_SUBMISSION_EMAILS : "<EMAIL>"
          REGISTER_NOW_FORM_SUBMISSION_EMAILS : "<EMAIL>"
 
  CSVExport:
    Type: "AWS::Serverless::Function"
    Properties:
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /csv
            Method: ANY
            Auth:
              Authorizer: NONE
      Handler: csv-exporter/index.handler
      Runtime: nodejs22.x
      Timeout: 300
      Environment:
        Variables:
          PROPERTY_ID: "URW-AIRPORT"
          CSV_FILE_PATH: "/var/tmp"
          NTO_EMAILS: "<EMAIL>"
          S3_BUCKET_NAME: "urw-airports-demo-contactus-responses"
          PRESIGNED_URL_EXPIRATION: 120
          CONTACT_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-contact-response"
          OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-opportunity-response"
          HOLT_EMAIL: "<EMAIL>"
          DAILY_EXPORT_EMAILS: "<EMAIL>"
          LEASING_EMAIL: "<EMAIL>"
          OPS_EMAIL: "<EMAIL>"
          CP_EMAIL: "<EMAIL>"
          SOURCE_EMAIL: "<EMAIL>"

  CSVExportWeekly:
    Type: "AWS::Serverless::Function"
    Properties:
      Handler: csv-exporter-weekly/index.handler
      Runtime: nodejs22.x
      Timeout: 300
      Environment:
        Variables:
          PROPERTY_ID: "URW-AIRPORT"
          CSV_FILE_PATH: "/var/tmp"
          S3_BUCKET_NAME: "urw-airports-demo-contactus-responses"
          PRESIGNED_URL_EXPIRATION: 120
          CONTACT_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-contact-response"
          OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-opportunity-response"
          DAILY_EXPORT_EMAILS: "<EMAIL>"
          SOURCE_EMAIL: "<EMAIL>"
