variable "is_enabled" {
  description = "Is rule enabled flag"
  type        = bool
}

variable "name" {
  description = "Event bridge rule name"
  type        = string
}

variable "schedule_expression" {
  description = "Schedule expression"
  type        = string
}

variable "lambda_arn" {
  description = "Event rule target lambda arn"
  type        = string
}

variable "input_json" {
  description = "JSON input to pass to the lambda function"
  type        = string
  default     = null
}
