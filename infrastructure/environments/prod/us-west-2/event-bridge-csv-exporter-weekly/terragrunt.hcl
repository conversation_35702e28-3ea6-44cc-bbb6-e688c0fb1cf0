## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  resource_prefix      = local.envvars.locals.resource_prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
}

## Dependencies
dependency "lambda_csv_exporter" {
  config_path = "../lambda-csv-exporter"
  mock_outputs = {
    arn = "arn:aws:lambda:us-west-2:123456789012:function:mock-function"
  }
}

## Providers
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "${local.aws_provider_version}"
    }
  }
}

provider "aws" {
  region = "us-west-2"
}
EOF
}

inputs = {
  name                = "${local.project}-${local.env}-schedule-trigger-csv-exporter-weekly-lambda"
  is_enabled          = true
  schedule_expression = "cron(0 0 ? * MON *)"  # Every Monday at midnight UTC (weekly)
  lambda_arn          = dependency.lambda_csv_exporter.outputs.arn
  input_json          = jsonencode({
    isWeeklyRun = true
  })
}
